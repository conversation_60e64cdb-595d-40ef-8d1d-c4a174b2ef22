import { json, error } from '@sveltejs/kit';
import { env } from '$env/dynamic/private';

/**
 * Get line items associated with a deal from HubSpot
 * @param {string} dealId - The HubSpot deal ID
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Array>} Array of line items with details
 */
async function getDealLineItemsWithDetails(dealId, accessToken) {
    try {
        // First, get all line item associations
        const associationsResponse = await fetch(
            `https://api.hubapi.com/crm/v4/objects/deals/${dealId}/associations/line_items`,
            {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        if (!associationsResponse.ok) {
            throw new Error(`Failed to fetch associations: ${associationsResponse.statusText}`);
        }

        const associationsData = await associationsResponse.json();
        const lineItemIds = associationsData.results.map(item => item.toObjectId);

        if (lineItemIds.length === 0) {
            return [];
        }

        // Batch fetch line item details (up to 100 at a time)
        const batchSize = 100;
        const batches = [];

        // Create batches of line item IDs
        for (let i = 0; i < lineItemIds.length; i += batchSize) {
            batches.push(lineItemIds.slice(i, i + batchSize));
        }

        // Fetch all batches in parallel
        const batchPromises = batches.map(batch =>
            fetch('https://api.hubapi.com/crm/v3/objects/line_items/batch/read', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    inputs: batch.map(id => ({ id })),
                    properties: ['name', 'price', 'quantity', 'hs_product_id', 'amount', 'hs_sku']
                })
            }).then(response => {
                if (!response.ok) {
                    throw new Error(`Batch request failed: ${response.statusText}`);
                }
                return response.json();
            })
        );

        const batchResults = await Promise.all(batchPromises);
        const allLineItems = batchResults.flatMap(result => result.results);

        return allLineItems;

    } catch (error) {
        console.error('Error fetching deal line items:', error);
        throw error;
    }
}

/**
 * Check if a line item with given SKU exists in the deal
 * @param {Array} lineItems - Array of line items
 * @param {string} sku - SKU to search for
 * @returns {Object|null} Line item object if found, null otherwise
 */
function findLineItemBySku(lineItems, sku) {
    return lineItems.find(item =>
        item.properties.hs_sku === sku ||
        item.properties.name === sku ||
        item.properties.hs_product_id === sku
    ) || null;
}

/**
 * Search for existing products in HubSpot product library by SKU
 * @param {string} sku - SKU to search for
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object|null>} Product object if found, null otherwise
 */
async function findProductBySku(sku, accessToken) {
    try {
        console.log('Searching for product with SKU:', sku);

        // Search products by SKU
        const response = await fetch(
            `https://api.hubapi.com/crm/v3/objects/products/search`,
            {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    filterGroups: [{
                        filters: [{
                            propertyName: 'hs_sku',
                            operator: 'EQ',
                            value: sku
                        }]
                    }],
                    properties: ['name', 'hs_sku', 'price', 'description', 'hs_product_id'],
                    limit: 1
                })
            }
        );

        if (!response.ok) {
            const errorData = await response.text();
            console.log(`Product search failed: ${response.statusText} - ${errorData}`);
            return null;
        }

        const searchResult = await response.json();
        console.log('Product search result:', searchResult);

        if (searchResult.results && searchResult.results.length > 0) {
            const product = searchResult.results[0];
            console.log('Found product:', product);
            return product;
        }

        console.log('No product found with SKU:', sku);
        return null;
    } catch (error) {
        console.error('Error searching for product:', error);
        return null;
    }
}

/**
 * Create a line item from an existing product
 * @param {Object} product - Product object from HubSpot
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Created line item object
 */
async function createLineItemFromProduct(product, accessToken) {
    try {
        console.log('Creating line item from product:', product.id);

        const response = await fetch('https://api.hubapi.com/crm/v3/objects/line_items', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                properties: {
                    name: product.properties.name || product.properties.hs_sku,
                    hs_sku: product.properties.hs_sku,
                    hs_product_id: product.id,
                    quantity: 1,
                    price: product.properties.price || 0
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to create line item from product: ${response.statusText} - ${errorData}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error creating line item from product:', error);
        throw error;
    }
}

/**
 * Create a new line item with given SKU (fallback method)
 * @param {string} sku - SKU for the new line item
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Created line item object
 */
async function createLineItem(sku, accessToken) {
    try {
        const response = await fetch('https://api.hubapi.com/crm/v3/objects/line_items', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                properties: {
                    name: sku,
                    hs_sku: sku,
                    quantity: 1,
                    price: 0
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to create line item: ${response.statusText} - ${errorData}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error creating line item:', error);
        throw error;
    }
}

/**
 * Associate a line item with a deal
 * @param {string} dealId - Deal ID
 * @param {string} lineItemId - Line item ID
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Association result
 */
async function associateLineItemWithDeal(dealId, lineItemId, accessToken) {
    try {
        console.log('Associating line item with deal:');
        console.log('Deal ID:', dealId);
        console.log('Line Item ID:', lineItemId);

        // Try using the v3 associations API instead
        const url = `https://api.hubapi.com/crm/v3/objects/deals/${dealId}/associations/line_items/${lineItemId}/deal_to_line_item`;
        console.log('Association URL:', url);

        const response = await fetch(url, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            const errorData = await response.text();
            console.log('V3 association failed, trying v4 batch method...');

            // Try v4 batch associations as fallback
            return await associateLineItemWithDealV4Batch(dealId, lineItemId, accessToken);
        }

        return await response.json();
    } catch (error) {
        console.error('Error associating line item with deal:', error);
        throw error;
    }
}

/**
 * Alternative association method using v4 batch API
 */
async function associateLineItemWithDealV4Batch(dealId, lineItemId, accessToken) {
    try {
        console.log('Trying v4 batch association method...');

        const response = await fetch('https://api.hubapi.com/crm/v4/associations/deals/line_items/batch/create', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                inputs: [{
                    from: { id: dealId },
                    to: { id: lineItemId },
                    types: [{
                        associationCategory: "HUBSPOT_DEFINED",
                        associationTypeId: 20
                    }]
                }]
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to associate line item with deal using v4 batch: ${response.statusText} - ${errorData}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error in v4 batch association:', error);
        throw error;
    }
}

/**
 * Delete a line item
 * @param {string} lineItemId - Line item ID to delete
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<void>}
 */
async function deleteLineItem(lineItemId, accessToken) {
    try {
        const response = await fetch(`https://api.hubapi.com/crm/v3/objects/line_items/${lineItemId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to delete line item: ${response.statusText} - ${errorData}`);
        }
    } catch (error) {
        console.error('Error deleting line item:', error);
        throw error;
    }
}

/**
 * SvelteKit POST endpoint handler for HubSpot line item operations based on VAT status
 * @param {import('@sveltejs/kit').RequestEvent} event - The request event
 * @returns {Promise<Response>} - JSON response with the operation result
 */
export async function POST({ request, url }) {
    // Check API key
    const apiKey = request.headers.get('x-api-key');
    const expectedApiKey = env.API_KEY;

    if (!apiKey || apiKey !== expectedApiKey) {
        throw error(401, 'Unauthorized: Invalid or missing API key');
    }

    // Get HubSpot access token from environment
    const accessToken = env.HUBSPOT_ACCESS_TOKEN;
    if (!accessToken) {
        throw error(500, 'Server Error: HubSpot access token not configured');
    }

    let requestData = {};

    // Try to get parameters from query string first
    const dealIdFromQuery = url.searchParams.get('dealId');
    const propertyValueFromQuery = url.searchParams.get('propertyValue');
    const propertyNameFromQuery = url.searchParams.get('propertyName');

    if (dealIdFromQuery && propertyValueFromQuery && propertyNameFromQuery) {
        // Use query parameters
        requestData = {
            dealId: dealIdFromQuery,
            propertyValue: propertyValueFromQuery,
            propertyName: propertyNameFromQuery
        };
    } else {
        // Try to get from JSON body
        try {
            const contentLength = request.headers.get('content-length');
            if (contentLength && parseInt(contentLength) > 0) {
                requestData = await request.json();
            } else {
                throw error(400, 'Bad Request: No data provided in query parameters or request body');
            }
        } catch (err) {
            throw error(400, 'Bad Request: Invalid JSON in request body or missing query parameters');
        }
    }

    const { dealId, propertyValue, propertyName } = requestData;

    // Validate required parameters
    if (!dealId) {
        throw error(400, 'Bad Request: dealId parameter is required');
    }
    if (!propertyValue) {
        throw error(400, 'Bad Request: propertyValue parameter is required');
    }
    if (!propertyName) {
        throw error(400, 'Bad Request: propertyName parameter is required');
    }

    try {
        // Check if this is a VAT status update
        if (propertyName !== 'vat___status_podatnika') {
            return json({
                success: true,
                message: 'Property is not VAT status, no line item changes needed',
                action: 'none'
            });
        }

        // Get existing line items for the deal
        const lineItems = await getDealLineItemsWithDetails(dealId, accessToken);

        // Check if BR00032 line item exists
        const targetSku = 'BR00032';
        const existingLineItem = findLineItemBySku(lineItems, targetSku);

        // Define VAT values that require the BR00032 line item
        const vatValuesRequiringLineItem = ['VAT EU', 'VAT 8', 'VAT 9M'];

        // Split propertyValue by semicolon and trim whitespace
        const propertyValues = propertyValue.split(';').map(value => value.trim());

        // Check if any of the property values match the VAT values requiring line item
        const shouldHaveLineItem = propertyValues.some(value =>
            vatValuesRequiringLineItem.includes(value)
        );

        if (shouldHaveLineItem) {
            // Property value requires BR00032 line item
            if (existingLineItem) {
                const matchingValues = propertyValues.filter(value =>
                    vatValuesRequiringLineItem.includes(value)
                );
                return json({
                    success: true,
                    message: `VAT status contains "${matchingValues.join(', ')}" which requires BR00032 line item, and it already exists`,
                    lineItem: existingLineItem,
                    action: 'none'
                });
            }

            // Need to create BR00032 line item
            // First, search for existing product in product library
            const existingProduct = await findProductBySku(targetSku, accessToken);
            let newLineItem;

            if (existingProduct) {
                console.log('Found existing product BR00032, creating line item from product...');
                // Create line item from existing product
                newLineItem = await createLineItemFromProduct(existingProduct, accessToken);
            } else {
                console.log('No existing product BR00032 found, creating new line item...');
                // Create new line item with basic properties
                newLineItem = await createLineItem(targetSku, accessToken);
            }

            console.log('Created line item:', newLineItem);
            console.log('Deal ID:', dealId);
            console.log('Line Item ID:', newLineItem.id);

            // Associate with deal
            await associateLineItemWithDeal(dealId, newLineItem.id, accessToken);

            const matchingValues = propertyValues.filter(value =>
                vatValuesRequiringLineItem.includes(value)
            );
            return json({
                success: true,
                message: `VAT status contains "${matchingValues.join(', ')}" which requires BR00032 line item. ${existingProduct ? 'Line item created from existing product' : 'New line item created'} and associated with deal successfully`,
                lineItem: newLineItem,
                product: existingProduct,
                action: 'created'
            });

        } else {
            // Property value does NOT require BR00032 line item
            if (!existingLineItem) {
                return json({
                    success: true,
                    message: `VAT status "${propertyValue}" does not contain values requiring BR00032 line item, and none exists`,
                    lineItem: null,
                    action: 'none'
                });
            }

            // Need to delete existing BR00032 line item
            await deleteLineItem(existingLineItem.id, accessToken);

            return json({
                success: true,
                message: `VAT status "${propertyValue}" does not contain values requiring BR00032 line item. Existing line item deleted successfully`,
                lineItem: existingLineItem,
                action: 'deleted'
            });
        }

    } catch (err) {
        console.error('Error processing HubSpot VAT-based line item operation:', err);
        return json({
            success: false,
            error: err.message
        }, { status: 500 });
    }
}
