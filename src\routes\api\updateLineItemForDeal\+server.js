import { json, error } from '@sveltejs/kit';
import { env } from '$env/dynamic/private';

/**
 * Get line items associated with a deal from HubSpot
 * @param {string} dealId - The HubSpot deal ID
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Array>} Array of line items with details
 */
async function getDealLineItemsWithDetails(dealId, accessToken) {
    try {
        // First, get all line item associations
        const associationsResponse = await fetch(
            `https://api.hubapi.com/crm/v4/objects/deals/${dealId}/associations/line_items`,
            {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        if (!associationsResponse.ok) {
            throw new Error(`Failed to fetch associations: ${associationsResponse.statusText}`);
        }

        const associationsData = await associationsResponse.json();
        const lineItemIds = associationsData.results.map(item => item.toObjectId);

        if (lineItemIds.length === 0) {
            return [];
        }

        // Batch fetch line item details (up to 100 at a time)
        const batchSize = 100;
        const batches = [];

        // Create batches of line item IDs
        for (let i = 0; i < lineItemIds.length; i += batchSize) {
            batches.push(lineItemIds.slice(i, i + batchSize));
        }

        // Fetch all batches in parallel
        const batchPromises = batches.map(batch =>
            fetch('https://api.hubapi.com/crm/v3/objects/line_items/batch/read', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    inputs: batch.map(id => ({ id })),
                    properties: ['name', 'price', 'quantity', 'hs_product_id', 'amount', 'hs_sku']
                })
            }).then(response => {
                if (!response.ok) {
                    throw new Error(`Batch request failed: ${response.statusText}`);
                }
                return response.json();
            })
        );

        const batchResults = await Promise.all(batchPromises);
        const allLineItems = batchResults.flatMap(result => result.results);

        return allLineItems;

    } catch (error) {
        console.error('Error fetching deal line items:', error);
        throw error;
    }
}

/**
 * Check if a line item with given SKU exists in the deal
 * @param {Array} lineItems - Array of line items
 * @param {string} sku - SKU to search for
 * @returns {Object|null} Line item object if found, null otherwise
 */
function findLineItemBySku(lineItems, sku) {
    return lineItems.find(item =>
        item.properties.hs_sku === sku ||
        item.properties.name === sku ||
        item.properties.hs_product_id === sku
    ) || null;
}

/**
 * Search for existing products in HubSpot product library by SKU
 * @param {string} sku - SKU to search for
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object|null>} Product object if found, null otherwise
 */
async function findProductBySku(sku, accessToken) {
    try {
        console.log('Searching for product with SKU:', sku);

        // Search products by SKU
        const response = await fetch(
            `https://api.hubapi.com/crm/v3/objects/products/search`,
            {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    filterGroups: [{
                        filters: [{
                            propertyName: 'hs_sku',
                            operator: 'EQ',
                            value: sku
                        }]
                    }],
                    properties: ['name', 'hs_sku', 'price', 'description', 'hs_product_id'],
                    limit: 1
                })
            }
        );

        if (!response.ok) {
            const errorData = await response.text();
            console.log(`Product search failed: ${response.statusText} - ${errorData}`);
            return null;
        }

        const searchResult = await response.json();
        console.log('Product search result:', searchResult);

        if (searchResult.results && searchResult.results.length > 0) {
            const product = searchResult.results[0];
            console.log('Found product:', product);
            return product;
        }

        console.log('No product found with SKU:', sku);
        return null;
    } catch (error) {
        console.error('Error searching for product:', error);
        return null;
    }
}

/**
 * Create a line item from an existing product
 * @param {Object} product - Product object from HubSpot
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Created line item object
 */
async function createLineItemFromProduct(product, accessToken) {
    try {
        console.log('Creating line item from product:', product.id);

        const response = await fetch('https://api.hubapi.com/crm/v3/objects/line_items', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                properties: {
                    name: product.properties.name || product.properties.hs_sku,
                    hs_sku: product.properties.hs_sku,
                    hs_product_id: product.id,
                    quantity: 1,
                    price: product.properties.price || 0
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to create line item from product: ${response.statusText} - ${errorData}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error creating line item from product:', error);
        throw error;
    }
}

/**
 * Create a new line item with given SKU (fallback method)
 * @param {string} sku - SKU for the new line item
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Created line item object
 */
async function createLineItem(sku, accessToken) {
    try {
        const response = await fetch('https://api.hubapi.com/crm/v3/objects/line_items', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                properties: {
                    name: sku,
                    hs_sku: sku,
                    quantity: 1,
                    price: 0
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to create line item: ${response.statusText} - ${errorData}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error creating line item:', error);
        throw error;
    }
}

/**
 * Create a new line item with given SKU and specific quantity
 * @param {string} sku - SKU for the new line item
 * @param {number} quantity - Quantity for the line item
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Created line item object
 */
async function createLineItemWithQuantity(sku, quantity, accessToken) {
    try {
        const response = await fetch('https://api.hubapi.com/crm/v3/objects/line_items', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                properties: {
                    name: sku,
                    hs_sku: sku,
                    quantity: quantity,
                    price: 0
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to create line item with quantity: ${response.statusText} - ${errorData}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error creating line item with quantity:', error);
        throw error;
    }
}

/**
 * Create a line item from an existing product with specific quantity
 * @param {Object} product - Product object from HubSpot
 * @param {number} quantity - Quantity for the line item
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Created line item object
 */
async function createLineItemFromProductWithQuantity(product, quantity, accessToken) {
    try {
        console.log('Creating line item from product with quantity:', product.id, quantity);

        const response = await fetch('https://api.hubapi.com/crm/v3/objects/line_items', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                properties: {
                    name: product.properties.name || product.properties.hs_sku,
                    hs_sku: product.properties.hs_sku,
                    hs_product_id: product.id,
                    quantity: quantity,
                    price: product.properties.price || 0
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to create line item from product with quantity: ${response.statusText} - ${errorData}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error creating line item from product with quantity:', error);
        throw error;
    }
}

/**
 * Update the quantity of an existing line item
 * @param {string} lineItemId - Line item ID to update
 * @param {number} quantity - New quantity
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Updated line item object
 */
async function updateLineItemQuantity(lineItemId, quantity, accessToken) {
    try {
        console.log(`Updating line item ${lineItemId} quantity to ${quantity}`);

        const response = await fetch(`https://api.hubapi.com/crm/v3/objects/line_items/${lineItemId}`, {
            method: 'PATCH',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                properties: {
                    quantity: quantity
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to update line item quantity: ${response.statusText} - ${errorData}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error updating line item quantity:', error);
        throw error;
    }
}

/**
 * Associate a line item with a deal
 * @param {string} dealId - Deal ID
 * @param {string} lineItemId - Line item ID
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Association result
 */
async function associateLineItemWithDeal(dealId, lineItemId, accessToken) {
    try {
        console.log('Associating line item with deal:');
        console.log('Deal ID:', dealId);
        console.log('Line Item ID:', lineItemId);

        // Try using the v3 associations API instead
        const url = `https://api.hubapi.com/crm/v3/objects/deals/${dealId}/associations/line_items/${lineItemId}/deal_to_line_item`;
        console.log('Association URL:', url);

        const response = await fetch(url, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            const errorData = await response.text();
            console.log('V3 association failed, trying v4 batch method...');

            // Try v4 batch associations as fallback
            return await associateLineItemWithDealV4Batch(dealId, lineItemId, accessToken);
        }

        return await response.json();
    } catch (error) {
        console.error('Error associating line item with deal:', error);
        throw error;
    }
}

/**
 * Alternative association method using v4 batch API
 */
async function associateLineItemWithDealV4Batch(dealId, lineItemId, accessToken) {
    try {
        console.log('Trying v4 batch association method...');

        const response = await fetch('https://api.hubapi.com/crm/v4/associations/deals/line_items/batch/create', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                inputs: [{
                    from: { id: dealId },
                    to: { id: lineItemId },
                    types: [{
                        associationCategory: "HUBSPOT_DEFINED",
                        associationTypeId: 20
                    }]
                }]
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to associate line item with deal using v4 batch: ${response.statusText} - ${errorData}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error in v4 batch association:', error);
        throw error;
    }
}

/**
 * Get deal properties from HubSpot
 * @param {string} dealId - The HubSpot deal ID
 * @param {string} accessToken - HubSpot access token
 * @param {Array} properties - Array of property names to fetch
 * @returns {Promise<Object>} Deal properties object
 */
async function getDealProperties(dealId, accessToken, properties) {
    try {
        const propertiesParam = properties.join(',');
        const response = await fetch(
            `https://api.hubapi.com/crm/v3/objects/deals/${dealId}?properties=${propertiesParam}`,
            {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        if (!response.ok) {
            throw new Error(`Failed to fetch deal properties: ${response.statusText}`);
        }

        const dealData = await response.json();
        return dealData.properties;
    } catch (error) {
        console.error('Error fetching deal properties:', error);
        throw error;
    }
}

/**
 * Delete a line item
 * @param {string} lineItemId - Line item ID to delete
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<void>}
 */
async function deleteLineItem(lineItemId, accessToken) {
    try {
        const response = await fetch(`https://api.hubapi.com/crm/v3/objects/line_items/${lineItemId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to delete line item: ${response.statusText} - ${errorData}`);
        }
    } catch (error) {
        console.error('Error deleting line item:', error);
        throw error;
    }
}

/**
 * Calculate optimal accounting packages based on document quantity and accounting type
 * @param {number} documentCount - Number of documents needed
 * @param {boolean} isFullAccounting - Whether it's full accounting or simplified
 * @returns {Array} Array of optimal package configurations
 */
function calculateOptimalAccountingPackages(documentCount, isFullAccounting) {
    console.log(`Calculating packages for ${documentCount} documents, full accounting: ${isFullAccounting}`);

    if (documentCount === 0) {
        return [];
    }

    // Define package configurations
    const packages = {
        simplified: {
            BASE: { sku: 'BR00007', price: 234, documents: 5, extraPrice: 6.90, maxExtra: 9 },
            SILVER: { sku: 'BR00008', price: 294, documents: 20, extraPrice: 5.90, maxExtra: 38 },
            GOLD: { sku: 'BR00009', price: 524, documents: 80, extraPrice: 4.90, maxExtra: 32 },
            PLATINUM: { sku: 'BR00010', price: 684, documents: 150, extraPrice: 3.90, maxExtra: Infinity }
        },
        full: {
            BASE: { sku: 'BR00003', price: 499, documents: 5, extraPrice: 12.90, maxExtra: 20 },
            SILVER: { sku: 'BR00004', price: 764, documents: 50, extraPrice: 9.90, maxExtra: 60 },
            GOLD: { sku: 'BR00005', price: 1374, documents: 110, extraPrice: 7.90, maxExtra: 88 },
            PLATINUM: { sku: 'BR00006', price: 2074, documents: 200, extraPrice: 6.90, maxExtra: Infinity }
        }
    };

    // Define additional packages
    const additionalPackages = {
        simplified: {
            goldPack50: { sku: 'BR00019', price: 179, documents: 50 }, // For GOLD
            platinumPack50: { sku: 'BR00020', price: 179, documents: 50 }, // For PLATINUM
            platinumPack200: { sku: 'BR00021', price: 679, documents: 200 } // For PLATINUM
        },
        full: {
            goldPack50: { sku: 'BR00027', price: 329, documents: 50 }, // For GOLD
            platinumPack50: { sku: 'BR00028', price: 329, documents: 50 }, // For PLATINUM
            platinumPack200: { sku: 'BR00029', price: 1190, documents: 200 } // For PLATINUM
        }
    };

    // Define individual document SKUs
    const individualDocumentSkus = {
        simplified: {
            BASE: 'BR00015',
            SILVER: 'BR00016',
            GOLD: 'BR00017',
            PLATINUM: 'BR00018'
        },
        full: {
            BASE: 'BR00022',
            SILVER: 'BR00023',
            GOLD: 'BR00024',
            PLATINUM: 'BR00025'
        }
    };

    const accountingType = isFullAccounting ? 'full' : 'simplified';
    const availablePackages = packages[accountingType];
    const additionalPacks = additionalPackages[accountingType];
    const individualSkus = individualDocumentSkus[accountingType];

    // Calculate cost for each base package
    const packageOptions = [];

    for (const [packageName, packageData] of Object.entries(availablePackages)) {
        const result = calculatePackageCost(documentCount, packageData, additionalPacks, individualSkus[packageName], packageName);
        packageOptions.push({
            packageName,
            ...result
        });
    }

    // Find the most cost-effective option
    const optimalOption = packageOptions.reduce((best, current) =>
        current.totalCost < best.totalCost ? current : best
    );

    console.log('Package options calculated:', packageOptions);
    console.log('Optimal option selected:', optimalOption);

    return optimalOption.items;
}

/**
 * Compare current accounting packages with optimal packages
 * @param {Array} currentItems - Current line items in the deal
 * @param {Array} optimalPackages - Optimal package configuration
 * @returns {boolean} True if packages match, false if they need updating
 */
function compareAccountingPackages(currentItems, optimalPackages) {
    console.log('Comparing current packages with optimal packages...');
    console.log('Current items:', currentItems.map(item => ({
        sku: item.properties.hs_sku,
        quantity: item.properties.quantity || 1
    })));
    console.log('Optimal packages:', optimalPackages);

    // If quantities don't match, packages don't match
    if (currentItems.length !== optimalPackages.length) {
        console.log('Package count mismatch');
        return false;
    }

    // Create maps for easier comparison
    const currentMap = new Map();
    currentItems.forEach(item => {
        const sku = item.properties.hs_sku || item.properties.name || '';
        const quantity = parseInt(item.properties.quantity) || 1;
        currentMap.set(sku, (currentMap.get(sku) || 0) + quantity);
    });

    const optimalMap = new Map();
    optimalPackages.forEach(pkg => {
        optimalMap.set(pkg.sku, (optimalMap.get(pkg.sku) || 0) + pkg.quantity);
    });

    // Compare each SKU and quantity
    for (const [sku, quantity] of optimalMap) {
        if (currentMap.get(sku) !== quantity) {
            console.log(`Mismatch for ${sku}: current=${currentMap.get(sku)}, optimal=${quantity}`);
            return false;
        }
    }

    // Check for extra items in current that shouldn't be there
    for (const [sku, quantity] of currentMap) {
        if (!optimalMap.has(sku)) {
            console.log(`Extra item found: ${sku} (quantity: ${quantity})`);
            return false;
        }
    }

    console.log('Packages match perfectly');
    return true;
}

/**
 * Calculate the total cost and items needed for a specific base package
 */
function calculatePackageCost(documentCount, basePackage, additionalPacks, individualSku, packageName) {
    const items = [];
    let remainingDocuments = documentCount;
    let totalCost = 0;

    // Add base package
    items.push({
        sku: basePackage.sku,
        quantity: 1,
        description: `${packageName} package (${basePackage.documents} documents)`,
        price: basePackage.price
    });
    totalCost += basePackage.price;
    remainingDocuments -= basePackage.documents;

    if (remainingDocuments <= 0) {
        return { items, totalCost };
    }

    // For PLATINUM packages, we can use additional packs
    if (packageName === 'PLATINUM') {
        // Use 200-document packs first (more cost-effective)
        const pack200Count = Math.floor(remainingDocuments / 200);
        if (pack200Count > 0) {
            items.push({
                sku: additionalPacks.platinumPack200.sku,
                quantity: pack200Count,
                description: `200-document additional packs for PLATINUM`,
                price: additionalPacks.platinumPack200.price
            });
            totalCost += pack200Count * additionalPacks.platinumPack200.price;
            remainingDocuments -= pack200Count * 200;
        }

        // Use 50-document packs for remaining
        const pack50Count = Math.floor(remainingDocuments / 50);
        if (pack50Count > 0) {
            items.push({
                sku: additionalPacks.platinumPack50.sku,
                quantity: pack50Count,
                description: `50-document additional packs for PLATINUM`,
                price: additionalPacks.platinumPack50.price
            });
            totalCost += pack50Count * additionalPacks.platinumPack50.price;
            remainingDocuments -= pack50Count * 50;
        }
    } else if (packageName === 'GOLD') {
        // For GOLD packages, we can use 50-document additional packs
        const pack50Count = Math.floor(remainingDocuments / 50);
        if (pack50Count > 0) {
            items.push({
                sku: additionalPacks.goldPack50.sku,
                quantity: pack50Count,
                description: `50-document additional packs for GOLD`,
                price: additionalPacks.goldPack50.price
            });
            totalCost += pack50Count * additionalPacks.goldPack50.price;
            remainingDocuments -= pack50Count * 50;
        }

        // Check if remaining documents are within individual document limit
        const maxExtraAllowed = basePackage.maxExtra;
        if (remainingDocuments <= maxExtraAllowed) {
            // Use individual documents
            if (remainingDocuments > 0) {
                items.push({
                    sku: individualSku,
                    quantity: remainingDocuments,
                    description: `Individual documents`,
                    price: basePackage.extraPrice
                });
                totalCost += remainingDocuments * basePackage.extraPrice;
                remainingDocuments = 0;
            }
        } else {
            // Exceeds limit, this package is not viable - set high cost
            totalCost = Infinity;
        }
    } else {
        // For non-PLATINUM packages, check if we can use individual documents within limit
        const maxExtraAllowed = basePackage.maxExtra;
        if (remainingDocuments <= maxExtraAllowed) {
            // Use individual documents
            items.push({
                sku: individualSku,
                quantity: remainingDocuments,
                description: `Individual documents`,
                price: basePackage.extraPrice
            });
            totalCost += remainingDocuments * basePackage.extraPrice;
            remainingDocuments = 0;
        } else {
            // Exceeds limit, this package is not viable - set high cost
            totalCost = Infinity;
        }
    }

    // Add any remaining individual documents for PLATINUM
    if (remainingDocuments > 0 && packageName === 'PLATINUM') {
        items.push({
            sku: individualSku,
            quantity: remainingDocuments,
            description: `Individual documents`,
            price: basePackage.extraPrice
        });
        totalCost += remainingDocuments * basePackage.extraPrice;
    }

    return { items, totalCost };
}

/**
 * SvelteKit POST endpoint handler for HubSpot line item operations based on VAT status
 * @param {import('@sveltejs/kit').RequestEvent} event - The request event
 * @returns {Promise<Response>} - JSON response with the operation result
 */
export async function POST({ request, url }) {
    // Check API key
    const apiKey = request.headers.get('x-api-key');
    const expectedApiKey = env.API_KEY;

    if (!apiKey || apiKey !== expectedApiKey) {
        throw error(401, 'Unauthorized: Invalid or missing API key');
    }

    // Get HubSpot access token from environment
    const accessToken = env.HUBSPOT_ACCESS_TOKEN;
    if (!accessToken) {
        throw error(500, 'Server Error: HubSpot access token not configured');
    }

    let requestData = {};

    // Try to get parameters from query string first
    const dealIdFromQuery = url.searchParams.get('dealId');
    const propertyValueFromQuery = url.searchParams.get('propertyValue');
    const propertyNameFromQuery = url.searchParams.get('propertyName');

    if (dealIdFromQuery && propertyValueFromQuery && propertyNameFromQuery) {
        // Use query parameters
        requestData = {
            dealId: dealIdFromQuery,
            propertyValue: propertyValueFromQuery,
            propertyName: propertyNameFromQuery
        };
    } else {
        // Try to get from JSON body
        try {
            const contentLength = request.headers.get('content-length');
            if (contentLength && parseInt(contentLength) > 0) {
                requestData = await request.json();
            } else {
                throw error(400, 'Bad Request: No data provided in query parameters or request body');
            }
        } catch (err) {
            throw error(400, 'Bad Request: Invalid JSON in request body or missing query parameters');
        }
    }

    const { dealId, propertyValue, propertyName } = requestData;

    // Validate required parameters
    if (!dealId) {
        throw error(400, 'Bad Request: dealId parameter is required');
    }
    if (!propertyValue) {
        throw error(400, 'Bad Request: propertyValue parameter is required');
    }
    if (!propertyName) {
        throw error(400, 'Bad Request: propertyName parameter is required');
    }

    try {
        // Define supported properties
        const vatProperty = 'vat___status_podatnika';
        const languageProperty = 'jezyk_obslugi';
        const accountingProperties = [
            'faktury_rachunki_sprzedazowe___ile_',
            'dokumenty_wewnetrzne_wdt__wnt_itp',
            'faktury_rachunki_zakupu___ile_',
            'faktury_walutowe___ile_miesiecznie_',
            'operacje_kp_kw_walutowe',
            'kp_kw___banki_',
            'kp_kw_gotowka'
        ];

        const isVatProperty = propertyName === vatProperty;
        const isLanguageProperty = propertyName === languageProperty;
        const isAccountingProperty = accountingProperties.includes(propertyName);

        // Check if this is a supported property update
        if (!isVatProperty && !isLanguageProperty && !isAccountingProperty) {
            return json({
                success: true,
                message: 'Property is not supported for line item changes',
                action: 'none'
            });
        }

        // TODO: Remove this testing condition - only process deals with description "cascasc"
        // This is for testing purposes only
        const dealProperties = await getDealProperties(dealId, accessToken, ['description']);
        const dealDescription = dealProperties.description || '';

        if (dealDescription !== 'cascasc') {
            console.log(`Skipping deal ${dealId} - description is "${dealDescription}", not "cascasc"`);
            return json({
                success: true,
                message: 'Deal skipped - not in testing mode (description must be "cascasc")',
                action: 'skipped',
                dealDescription
            });
        }

        console.log(`Processing deal ${dealId} - description matches "cascasc"`);

        // Get existing line items for the deal
        const lineItems = await getDealLineItemsWithDetails(dealId, accessToken);
        console.log(`Found ${lineItems.length} line items for deal ${dealId}`);

        // Initialize line item requirements
        let shouldHaveBR00032 = false;
        let shouldHaveBR00033 = false;
        let shouldHaveBR00129 = false;
        let br00013Quantity = 0;
        let br00013BankStatementQuantity = 0;

        // Handle accounting properties workflow
        if (isAccountingProperty) {
            console.log('Processing accounting property:', propertyName);

            // Get all relevant deal properties
            const requiredProperties = [
                'faktury_rachunki_sprzedazowe___ile_',
                'dokumenty_wewnetrzne_wdt__wnt_itp',
                'faktury_rachunki_zakupu___ile_',
                'faktury_walutowe___ile_miesiecznie_',
                'operacje_kp_kw_walutowe',
                'kp_kw___banki_',
                'kp_kw_gotowka',
                'rodzaj_ksiegowosci'
            ];

            const dealProperties = await getDealProperties(dealId, accessToken, requiredProperties);
            console.log('Deal properties:', dealProperties);

            // Calculate cash-bank operations sum
            const cashBankOperations = [
                'operacje_kp_kw_walutowe',
                'kp_kw___banki_',
                'kp_kw_gotowka'
            ];

            const cashBankSum = cashBankOperations.reduce((sum, prop) => {
                const value = parseInt(dealProperties[prop]) || 0;
                console.log(`${prop}: ${value}`);
                return sum + value;
            }, 0);

            // Calculate bookings sum
            const bookingOperations = [
                'faktury_rachunki_sprzedazowe___ile_',
                'faktury_rachunki_zakupu___ile_',
                'faktury_walutowe___ile_miesiecznie_',
                'dokumenty_wewnetrzne_wdt__wnt_itp'
            ];

            const bookingsSum = bookingOperations.reduce((sum, prop) => {
                const value = parseInt(dealProperties[prop]) || 0;
                console.log(`${prop}: ${value}`);
                return sum + value;
            }, 0);

            console.log('Cash-bank operations sum:', cashBankSum);
            console.log('Bookings sum:', bookingsSum);

            // Check accounting type (case-insensitive)
            const accountingType = dealProperties['rodzaj_ksiegowosci'] || '';
            const isFullAccounting = accountingType.toLowerCase().includes('pełna księgowość');
            console.log('Accounting type:', accountingType);
            console.log('Is full accounting:', isFullAccounting);

            // Determine BR00013 quantity
            if (isFullAccounting) {
                // For full accounting: use max of cash-bank and bookings
                br00013Quantity = Math.max(cashBankSum, bookingsSum);
            } else {
                // For non-full accounting: use only bookings sum
                br00013Quantity = bookingsSum;
            }

            console.log('BR00013 quantity needed:', br00013Quantity);

            // Calculate optimal accounting packages
            const accountingPackages = calculateOptimalAccountingPackages(br00013Quantity, isFullAccounting);
            console.log('Optimal accounting packages:', accountingPackages);

            // Calculate BR00013 quantity (for bank statement processing)
            // This should equal the bookings sum used in the calculation
            br00013BankStatementQuantity = bookingsSum;
            console.log('BR00013 bank statement quantity needed:', br00013BankStatementQuantity);
        }

        if (propertyName === 'vat___status_podatnika') {
            // Handle empty or null VAT status (should remove all VAT line items)
            if (!propertyValue || propertyValue.trim() === '') {
                console.log('VAT status is empty - removing all VAT line items');
                shouldHaveBR00032 = false;
                shouldHaveBR00033 = false;
            } else {
                // Split propertyValue by semicolon and trim whitespace for VAT status
                const propertyValues = propertyValue.split(';').map(value => value.trim()).filter(value => value !== '');
                console.log('VAT property values after split:', propertyValues);

                // Define VAT values that require specific line items
                const vatValuesRequiringBR00032 = ['VAT EU', 'VAT 8', 'VAT 9M'];
                const vatValuesRequiringBR00033 = ['VAT OSS'];

                // Check which line items should exist based on VAT status
                shouldHaveBR00032 = propertyValues.some(value =>
                    vatValuesRequiringBR00032.includes(value)
                );
                shouldHaveBR00033 = propertyValues.some(value =>
                    vatValuesRequiringBR00033.includes(value)
                );
            }

            console.log('VAT-based requirements - BR00032:', shouldHaveBR00032, 'BR00033:', shouldHaveBR00033);

            // For VAT property changes, preserve existing language settings
            // Get current language setting to maintain BR00129 status
            const currentLanguageProps = await getDealProperties(dealId, accessToken, ['jezyk_obslugi']);
            const currentLanguage = currentLanguageProps['jezyk_obslugi'] || '';
            // Empty language is treated as non-Polish, so BR00129 should exist
            shouldHaveBR00129 = !currentLanguage || currentLanguage.trim() === '' || currentLanguage !== 'Polski';
            console.log('Preserving language setting - BR00129:', shouldHaveBR00129, 'Language:', currentLanguage);
        }

        if (propertyName === 'jezyk_obslugi') {
            // Handle language property
            console.log('Language property value:', propertyValue);

            // If language is empty, null, or NOT "Polski", add BR00129
            // Empty language is treated as non-Polish
            shouldHaveBR00129 = !propertyValue || propertyValue.trim() === '' || propertyValue !== 'Polski';

            console.log('Language-based requirement - BR00129:', shouldHaveBR00129);

            // For language property changes, preserve existing VAT settings
            // Get current VAT setting to maintain BR00032/BR00033 status
            const currentVatProps = await getDealProperties(dealId, accessToken, ['vat___status_podatnika']);
            const currentVatStatus = currentVatProps['vat___status_podatnika'] || '';

            if (!currentVatStatus || currentVatStatus.trim() === '') {
                // No VAT status set, so no VAT line items should exist
                shouldHaveBR00032 = false;
                shouldHaveBR00033 = false;
                console.log('No current VAT status - no VAT line items needed');
            } else {
                const currentVatValues = currentVatStatus.split(';').map(value => value.trim()).filter(value => value !== '');

                const vatValuesRequiringBR00032 = ['VAT EU', 'VAT 8', 'VAT 9M'];
                const vatValuesRequiringBR00033 = ['VAT OSS'];

                shouldHaveBR00032 = currentVatValues.some(value => vatValuesRequiringBR00032.includes(value));
                shouldHaveBR00033 = currentVatValues.some(value => vatValuesRequiringBR00033.includes(value));

                console.log('Preserving VAT settings - BR00032:', shouldHaveBR00032, 'BR00033:', shouldHaveBR00033, 'VAT Status:', currentVatStatus);
            }
        }

        // Check existing line items
        const existingBR00032 = findLineItemBySku(lineItems, 'BR00032');
        const existingBR00033 = findLineItemBySku(lineItems, 'BR00033');
        const existingBR00129 = findLineItemBySku(lineItems, 'BR00129');

        console.log('Existing line items:');
        console.log('- BR00032:', existingBR00032 ? existingBR00032.id : 'none');
        console.log('- BR00033:', existingBR00033 ? existingBR00033.id : 'none');
        console.log('- BR00129:', existingBR00129 ? existingBR00129.id : 'none');

        // Handle BR00032 line item
        if (shouldHaveBR00032) {
            if (!existingBR00032) {
                console.log('Creating BR00032 line item...');
                const existingProduct = await findProductBySku('BR00032', accessToken);
                let newLineItem;

                if (existingProduct) {
                    console.log('Found existing product BR00032, creating line item from product...');
                    newLineItem = await createLineItemFromProduct(existingProduct, accessToken);
                } else {
                    console.log('No existing product BR00032 found, creating new line item...');
                    newLineItem = await createLineItem('BR00032', accessToken);
                }

                await associateLineItemWithDeal(dealId, newLineItem.id, accessToken);
                console.log('BR00032 line item created and associated');
            } else {
                console.log('BR00032 line item already exists');
            }
        } else {
            if (existingBR00032) {
                console.log('Deleting existing BR00032 line item...');
                await deleteLineItem(existingBR00032.id, accessToken);
                console.log('BR00032 line item deleted');
            }
        }

        // Handle BR00033 line item
        if (shouldHaveBR00033) {
            if (!existingBR00033) {
                console.log('Creating BR00033 line item...');
                const existingProduct = await findProductBySku('BR00033', accessToken);
                let newLineItem;

                if (existingProduct) {
                    console.log('Found existing product BR00033, creating line item from product...');
                    newLineItem = await createLineItemFromProduct(existingProduct, accessToken);
                } else {
                    console.log('No existing product BR00033 found, creating new line item...');
                    newLineItem = await createLineItem('BR00033', accessToken);
                }

                await associateLineItemWithDeal(dealId, newLineItem.id, accessToken);
                console.log('BR00033 line item created and associated');
            } else {
                console.log('BR00033 line item already exists');
            }
        } else {
            if (existingBR00033) {
                console.log('Deleting existing BR00033 line item...');
                await deleteLineItem(existingBR00033.id, accessToken);
                console.log('BR00033 line item deleted');
            }
        }

        // Handle BR00129 line item (language-based)
        if (shouldHaveBR00129) {
            if (!existingBR00129) {
                console.log('Creating BR00129 line item...');
                const existingProduct = await findProductBySku('BR00129', accessToken);
                let newLineItem;

                if (existingProduct) {
                    console.log('Found existing product BR00129, creating line item from product...');
                    newLineItem = await createLineItemFromProduct(existingProduct, accessToken);
                } else {
                    console.log('No existing product BR00129 found, creating new line item...');
                    newLineItem = await createLineItem('BR00129', accessToken);
                }

                await associateLineItemWithDeal(dealId, newLineItem.id, accessToken);
                console.log('BR00129 line item created and associated');
            } else {
                console.log('BR00129 line item already exists');
            }
        } else {
            if (existingBR00129) {
                console.log('Deleting existing BR00129 line item...');
                await deleteLineItem(existingBR00129.id, accessToken);
                console.log('BR00129 line item deleted');
            }
        }

        // Handle accounting packages (incremental approach)
        if (isAccountingProperty) {
            console.log(`Processing accounting packages. Document quantity: ${br00013Quantity}`);

            // Always remove legacy BR00013 items
            const legacyBR00013Items = lineItems.filter(item =>
                item.properties.hs_sku === 'BR00013' ||
                item.properties.name === 'BR00013' ||
                item.properties.hs_product_id === 'BR00013'
            );

            console.log(`Found ${legacyBR00013Items.length} legacy BR00013 line items to remove`);

            for (const item of legacyBR00013Items) {
                console.log(`Deleting legacy BR00013 line item: ${item.id}`);
                await deleteLineItem(item.id, accessToken);
            }

            // Get accounting type
            const dealPropsForAccounting = await getDealProperties(dealId, accessToken, ['rodzaj_ksiegowosci']);
            const accountingTypeForPackages = dealPropsForAccounting['rodzaj_ksiegowosci'] || '';
            const isFullAccountingForPackages = accountingTypeForPackages.toLowerCase().includes('pełna księgowość');

            // Calculate optimal packages for current document count
            const optimalPackages = calculateOptimalAccountingPackages(br00013Quantity, isFullAccountingForPackages);
            console.log('Optimal packages for current calculation:', optimalPackages);

            if (br00013Quantity > 0 && optimalPackages.length > 0) {
                // Find existing accounting packages that match our optimal configuration
                const currentAccountingItems = lineItems.filter(item => {
                    const sku = item.properties.hs_sku || item.properties.name || '';
                    return [
                        'BR00003', 'BR00004', 'BR00005', 'BR00006', // Full accounting packages
                        'BR00007', 'BR00008', 'BR00009', 'BR00010', // Simplified accounting packages
                        'BR00015', 'BR00016', 'BR00017', 'BR00018', // Simplified individual documents
                        'BR00019', 'BR00020', 'BR00021', // Simplified additional packs
                        'BR00022', 'BR00023', 'BR00024', 'BR00025', // Full individual documents
                        'BR00027', 'BR00028', 'BR00029' // Full additional packs
                    ].includes(sku);
                });

                console.log(`Found ${currentAccountingItems.length} existing accounting packages`);

                // Check what we need vs what we have for each optimal package
                for (const optimalPackage of optimalPackages) {
                    const existingItem = currentAccountingItems.find(item =>
                        (item.properties.hs_sku || item.properties.name || '') === optimalPackage.sku
                    );

                    if (existingItem) {
                        const currentQuantity = parseInt(existingItem.properties.quantity) || 1;
                        const neededQuantity = optimalPackage.quantity;

                        if (currentQuantity !== neededQuantity) {
                            console.log(`Updating ${optimalPackage.sku} quantity from ${currentQuantity} to ${neededQuantity}`);
                            await updateLineItemQuantity(existingItem.id, neededQuantity, accessToken);
                        } else {
                            console.log(`${optimalPackage.sku} already has correct quantity (${currentQuantity})`);
                        }
                    } else {
                        console.log(`Creating new line item: ${optimalPackage.sku} x${optimalPackage.quantity}`);

                        const existingProduct = await findProductBySku(optimalPackage.sku, accessToken);
                        let newLineItem;

                        if (existingProduct) {
                            console.log(`Found existing product ${optimalPackage.sku}, creating line item from product...`);
                            newLineItem = await createLineItemFromProductWithQuantity(existingProduct, optimalPackage.quantity, accessToken);
                        } else {
                            console.log(`No existing product ${optimalPackage.sku} found, creating new line item...`);
                            newLineItem = await createLineItemWithQuantity(optimalPackage.sku, optimalPackage.quantity, accessToken);
                        }

                        await associateLineItemWithDeal(dealId, newLineItem.id, accessToken);
                        console.log(`Line item ${optimalPackage.sku} created and associated successfully`);
                    }
                }

                // Remove any accounting packages that are no longer needed
                // (Only remove packages that are NOT in our optimal configuration)
                const optimalSkus = optimalPackages.map(pkg => pkg.sku);
                const itemsToRemove = currentAccountingItems.filter(item => {
                    const sku = item.properties.hs_sku || item.properties.name || '';
                    return !optimalSkus.includes(sku);
                });

                for (const item of itemsToRemove) {
                    console.log(`Removing unneeded accounting item: ${item.id} (${item.properties.hs_sku})`);
                    await deleteLineItem(item.id, accessToken);
                }

                console.log('Accounting packages updated incrementally');
            } else if (br00013Quantity === 0) {
                // Remove all accounting packages if no documents needed
                const currentAccountingItems = lineItems.filter(item => {
                    const sku = item.properties.hs_sku || item.properties.name || '';
                    return [
                        'BR00003', 'BR00004', 'BR00005', 'BR00006', // Full accounting packages
                        'BR00007', 'BR00008', 'BR00009', 'BR00010', // Simplified accounting packages
                        'BR00015', 'BR00016', 'BR00017', 'BR00018', // Simplified individual documents
                        'BR00019', 'BR00020', 'BR00021', // Simplified additional packs
                        'BR00022', 'BR00023', 'BR00024', 'BR00025', // Full individual documents
                        'BR00027', 'BR00028', 'BR00029' // Full additional packs
                    ].includes(sku);
                });

                for (const item of currentAccountingItems) {
                    console.log(`Removing accounting item (zero documents): ${item.id} (${item.properties.hs_sku})`);
                    await deleteLineItem(item.id, accessToken);
                }

                console.log('All accounting packages removed (zero documents)');
            }

            // Handle BR00013 bank statement processing (separate from accounting packages)
            if (br00013BankStatementQuantity > 0) {
                console.log(`Processing BR00013 bank statement processing. Quantity needed: ${br00013BankStatementQuantity}`);

                // Find existing BR00013 line item
                const existingBR00013BankStatement = findLineItemBySku(lineItems, 'BR00013');

                if (existingBR00013BankStatement) {
                    const currentQuantity = parseInt(existingBR00013BankStatement.properties.quantity) || 1;
                    if (currentQuantity !== br00013BankStatementQuantity) {
                        console.log(`Updating BR00013 bank statement quantity from ${currentQuantity} to ${br00013BankStatementQuantity}`);
                        await updateLineItemQuantity(existingBR00013BankStatement.id, br00013BankStatementQuantity, accessToken);
                    } else {
                        console.log(`BR00013 bank statement already has correct quantity (${currentQuantity})`);
                    }
                } else {
                    console.log(`Creating new BR00013 bank statement line item with quantity ${br00013BankStatementQuantity}`);

                    const existingProduct = await findProductBySku('BR00013', accessToken);
                    let newLineItem;

                    if (existingProduct) {
                        console.log('Found existing product BR00013, creating line item from product...');
                        newLineItem = await createLineItemFromProductWithQuantity(existingProduct, br00013BankStatementQuantity, accessToken);
                    } else {
                        console.log('No existing product BR00013 found, creating new line item...');
                        newLineItem = await createLineItemWithQuantity('BR00013', br00013BankStatementQuantity, accessToken);
                    }

                    await associateLineItemWithDeal(dealId, newLineItem.id, accessToken);
                    console.log('BR00013 bank statement line item created and associated successfully');
                }
            } else if (isAccountingProperty) {
                // Remove BR00013 if no bookings needed
                const existingBR00013BankStatement = findLineItemBySku(lineItems, 'BR00013');
                if (existingBR00013BankStatement) {
                    console.log('Removing BR00013 bank statement (zero bookings)');
                    await deleteLineItem(existingBR00013BankStatement.id, accessToken);
                }
            }
        }

        // Prepare response
        const actions = [];
        if (shouldHaveBR00032 && !existingBR00032) actions.push('Created BR00032');
        if (!shouldHaveBR00032 && existingBR00032) actions.push('Deleted BR00032');
        if (shouldHaveBR00033 && !existingBR00033) actions.push('Created BR00033');
        if (!shouldHaveBR00033 && existingBR00033) actions.push('Deleted BR00033');
        if (shouldHaveBR00129 && !existingBR00129) actions.push('Created BR00129');
        if (!shouldHaveBR00129 && existingBR00129) actions.push('Deleted BR00129');
        if (isAccountingProperty && br00013Quantity > 0) {
            // Get accounting type for response
            const dealPropsForResponse = await getDealProperties(dealId, accessToken, ['rodzaj_ksiegowosci']);
            const accountingTypeForResponse = dealPropsForResponse['rodzaj_ksiegowosci'] || '';
            const isFullAccountingForResponse = accountingTypeForResponse.toLowerCase().includes('pełna księgowość');

            const accountingPackages = calculateOptimalAccountingPackages(br00013Quantity, isFullAccountingForResponse);
            actions.push(`Created optimal accounting packages for ${br00013Quantity} documents`);
        }
        if (isAccountingProperty && br00013Quantity === 0) actions.push('Removed all accounting packages');

        if (isAccountingProperty && br00013BankStatementQuantity > 0) {
            const existingBR00013 = findLineItemBySku(lineItems, 'BR00013');
            if (existingBR00013) {
                actions.push(`Updated BR00013 bank statement quantity to ${br00013BankStatementQuantity}`);
            } else {
                actions.push(`Created BR00013 bank statement with quantity ${br00013BankStatementQuantity}`);
            }
        }
        if (isAccountingProperty && br00013BankStatementQuantity === 0) actions.push('Removed BR00013 bank statement');

        const actionMessage = actions.length > 0 ? actions.join(', ') : 'No changes needed';

        let processingType;
        if (isVatProperty) processingType = 'VAT status';
        else if (isLanguageProperty) processingType = 'Language';
        else if (isAccountingProperty) processingType = 'Accounting';
        else processingType = 'Property';

        const response = {
            success: true,
            message: `${processingType} processing completed. ${actionMessage}`,
            actions: {
                BR00032: shouldHaveBR00032 ? (existingBR00032 ? 'exists' : 'created') : (existingBR00032 ? 'deleted' : 'none'),
                BR00033: shouldHaveBR00033 ? (existingBR00033 ? 'exists' : 'created') : (existingBR00033 ? 'deleted' : 'none'),
                BR00129: shouldHaveBR00129 ? (existingBR00129 ? 'exists' : 'created') : (existingBR00129 ? 'deleted' : 'none')
            },
            propertyName,
            propertyValue
        };

        // Add accounting packages info for accounting properties
        if (isAccountingProperty) {
            if (br00013Quantity > 0) {
                // Get accounting type for final response
                const dealPropsForFinalResponse = await getDealProperties(dealId, accessToken, ['rodzaj_ksiegowosci']);
                const accountingTypeForFinalResponse = dealPropsForFinalResponse['rodzaj_ksiegowosci'] || '';
                const isFullAccountingForFinalResponse = accountingTypeForFinalResponse.toLowerCase().includes('pełna księgowość');

                const accountingPackages = calculateOptimalAccountingPackages(br00013Quantity, isFullAccountingForFinalResponse);
                response.actions.accountingPackages = `optimized_for_${br00013Quantity}_documents`;
                response.accountingPackages = accountingPackages;
                response.documentQuantity = br00013Quantity;
                response.accountingType = isFullAccountingForFinalResponse ? 'Pełna księgowość' : 'Księgowość uproszona';
            } else {
                response.actions.accountingPackages = 'removed_all';
                response.documentQuantity = 0;
            }

            // Add BR00013 bank statement info
            if (br00013BankStatementQuantity > 0) {
                const existingBR00013 = findLineItemBySku(lineItems, 'BR00013');
                response.actions.BR00013 = existingBR00013 ? `updated_quantity_${br00013BankStatementQuantity}` : `created_quantity_${br00013BankStatementQuantity}`;
                response.br00013BankStatementQuantity = br00013BankStatementQuantity;
            } else {
                response.actions.BR00013 = 'removed';
                response.br00013BankStatementQuantity = 0;
            }
        }

        return json(response);

    } catch (err) {
        console.error('Error processing HubSpot VAT-based line item operation:', err);
        return json({
            success: false,
            error: err.message
        }, { status: 500 });
    }
}
